const closeButton = document.querySelector(".main__genisis-header-container-right-close-container");
const garage = document.querySelector(".main__genisis-header-btn-1");
const around = document.querySelector(".main__genisis-header-btn-2");
const impound = document.querySelector(".main__genisis-header-btn-4");
const favorites = document.querySelector(".main__genisis-header-btn-3");

const carList = ".main__genisis-garage-scroll-container";

let impoundPrice = 1000;
let isImpound = false;
let category = "garage";

function actionLabel() {
    if (isImpound) {
        return `Impound for ${impoundPrice} €`;
    } if (category === "garage" || category === "favourites") {
        return "Ausparken";
    } else if (category === "around") {
        return "Auswählen";
    }
}

function carComponent({ label, nickname, modelName, plate, favourite }) {
    return `
    <div class="main__genisis-garage-scroll-item">
        <div class="main__genisis-garage-item-window-1">
            <div class="main__genisis-garage-scroll-item-header">
                <div class="main__genisis-garage-scroll-item-header-left">
                    <p id="name-display">${nickname ?? label}</p>
                    <p>${plate}</p>
                </div>
                <div class="main__genisis-garage-scroll-item-header-right-container">
                    <div id="rename" class="main__genisis-garage-scroll-item-header-right">
                        <i class="fa-solid fa-pencil" aria-hidden="true"></i>
                    </div>
                    <div id="favorite" class="main__genisis-garage-scroll-item-header-right ${favourite && "active"}">
                        <i class="fa-solid fa-star" aria-hidden="true"></i>
                    </div>
                </div>
            </div>
            <div class="main__genisis-garage-scroll-item-img">
                <img loading="lazy" src="https://sc-cdn.castillo.zone/garage/${modelName}.png" alt="${modelName} icon" />
            </div>
            <div class="main__genisis-garage-scroll-btn">
                <p>${actionLabel()}</p>
            </div>
        </div>
    </div>
    `;
}

function insertVehicle(vehicle) {
    const component = carComponent(vehicle);
    const self = $(component).appendTo(carList);

    self
    .find("#rename")
    .on("click", function() {
        const p = self.find("#name-display");
        const prevName = p.text();
        const input = $("<input>", {
            type: "text",
            class: "rename-input",
            id: "name-input",
            placeholder: prevName,
        });

        input.on("keypress", function(e) {
            if (e.which === 13) {
                $(this).blur();
            }
        });

        input.on("blur", function() {
            let newName = $(this).val();
            if (newName === "") {
                newName = prevName;
            }

            const newP = $("<p>", {
                id: "name-display",
                text: newName,
            });

            $(this).replaceWith(newP);

            if (prevName == newName) {
                return;
            }

            $.post(
                `https://${GetParentResourceName()}/changenickname`,
                JSON.stringify({
                    plate: vehicle.plate,
                    nickname: newName,
                }),
            );
        });

        p.replaceWith(input);
        input.focus();
    });

    self
    .find("#favorite")
    .on("click", function() {
        vehicle.favourite = !vehicle.favourite;
        if (vehicle.favourite) {
            $(this).addClass("active");
        } else {
            $(this).removeClass("active");

            if (category === "favourites") {
                self.remove();
            }
        }

        $.post(
            `https://${GetParentResourceName()}/setfavourite`,
            JSON.stringify({ plate: vehicle.plate }),
        );
    });

    self
    .find(".main__genisis-garage-scroll-btn")
    .on("click", function() {
        if (category === "garage" || category === "favourites") {
            $.post(
                `https://${GetParentResourceName()}/parkoutvehicle`,
                JSON.stringify({ vehicle }),
            );
            closeUi();
        } else if (category === "around") {
            $.post(
                `https://${GetParentResourceName()}/parkinvehicle`,
                JSON.stringify({ vehicle }),
            );
        } else {
            $.post(
                `https://${GetParentResourceName()}/takefromimpound`,
                JSON.stringify({ vehicle }),
            );
        }
    });
}

window.addEventListener("message", (event) => {
    const data = event.data;

    if (data.action === "OpenMenu") {
        $("body").fadeIn();
        category = "garage";
        impoundPrice = data.impoundprice;
    } else if (data.action === "LoadVehicles") {
        isImpound = data.locsort === "impound";

        if (isImpound) {
            $(".main__genisis-header-btn-container").css("display", "none");
            $("#location-sort").text("Towing");
        } else {
            $(".main__genisis-header-btn-container").css("display", "flex");
            $("#location-sort").text("Garage");
        }

        $(carList).empty();
        data.vehicles.forEach(v => insertVehicle(v));
    }
});

function closeUi() {
    $("body").fadeOut();
    $.post(`https://${GetParentResourceName()}/exit`);
}

closeButton.addEventListener("click", closeUi);
window.addEventListener("keyup", (event) => {
    if (event.key === "Escape") {
        closeUi();
    }
});

function updateCategory() {
    $.post(
        `https://${GetParentResourceName()}/switchcategory`,
        JSON.stringify({ category }),
    );
}

garage.addEventListener("click", () => {
    category = "garage";
    updateCategory();
});

around.addEventListener("click", () => {
    category = "around";
    updateCategory();
});

favorites.addEventListener("click", () => {
    category = "favourites";
    updateCategory();
});

// app = new Vue({
//     el: '.container',
//     data() {
//         return {
//             search: "",
//             currcategory: "garage",
//             locsort: "garage",
//             vehicles: [],
//             impoundprice: 0,
//         }
//     },

//     computed: {
//         GetData() {
//             let vehicles = this.vehicles

//             if (this.search != '' && this.search) {
//                 vehicles = vehicles.filter((item) => {
//                     const plate = item.plate.toUpperCase()
//                     const label = item.label.toUpperCase()
//                     const nickname = item.nickname ? item.nickname.toUpperCase() : ''

//                     if (plate.includes(this.search.toUpperCase())) {
//                         return true
//                     } else if (label.includes(this.search.toUpperCase())) {
//                         return true
//                     } else if (nickname.includes(this.search.toUpperCase())) {
//                         return true
//                     }
//                 })
//             }

//             return vehicles
//         },
//     },

//     methods: {
//         SwitchCategory(category) {
//             if (this.locsort == "impound") {
//                 return
//             }

//             if (category == "favourites" && this.locsort == "favourites") {
//                 category = "garage"
//             }

//             $.post(`https://${GetParentResourceName()}/switchcategory`, JSON.stringify({
//                 category: category,
//             })).then((response) => {
//                 if (response) {
//                     this.locsort = category
//                     this.currcategory = category
//                 } else {
//                     this.currcategory = "garage"
//                     $.post(`https://${GetParentResourceName()}/switchcategory`, JSON.stringify({
//                         category: "garage",
//                     }))
//                 }
//             })
//         },

//         OnEditName(vehicle) {
//             const index = this.vehicles.findIndex((item) => item.plate == vehicle.plate)
//             const element = document.getElementById(`vehicle_${index}`);

//             if (!element.hasAttribute("readonly")) {
//                 element.value = this.vehicles[index].nickname ? this.vehicles[index].nickname : this.vehicles[index].label;
//                 element.setAttribute("readonly", true);
//                 return;
//             }
        
//             element.removeAttribute("readonly");
//             element.focus();
//         },

//         OnSaveName(vehicle) {
//             const index = this.vehicles.findIndex((item) => item.plate == vehicle.plate)
//             const element = document.getElementById(`vehicle_${index}`);

//             $.post(`https://${GetParentResourceName()}/changenickname`, JSON.stringify({
//                 nickname: element.value ? element.value: null,
//                 plate: this.vehicles[index].plate,
//             })).then((response) => {
//                 if (response) {
//                     this.vehicles[index].nickname = element.value;
//                     element.setAttribute("readonly", true);
//                 } else {
//                     element.value = this.vehicles[index].nickname ? this.vehicles[index].nickname : this.vehicles[index].label;
//                     element.setAttribute("readonly", true);
//                 }
//             })
//         },

//         OnVehicleButton(vehicle, event) {
//             if (event.target.closest('.row_c')) {
//                 return
//             }

//             if (event.target.closest('.flex_functs')) {
//                 return
//             }

//             const index = this.vehicles.findIndex((item) => item.plate == vehicle.plate)
//             this.Exit()

//             if (this.locsort == "garage" || this.locsort == "favourites") {
//                 $.post(`https://${GetParentResourceName()}/parkoutvehicle`, JSON.stringify({
//                     vehicle: this.vehicles[index],
//                 }))
//             } else if (this.locsort == "impound") {
//                 $.post(`https://${GetParentResourceName()}/takefromimpound`, JSON.stringify({
//                     vehicle: this.vehicles[index],
//                 }))
//             } else if (this.locsort == "around") {
//                 $.post(`https://${GetParentResourceName()}/parkinvehicle`, JSON.stringify({
//                     vehicle: this.vehicles[index],
//                 }))
//             }
//         },

//         OnFavourite(vehicle) {
//             const index = this.vehicles.findIndex((item) => item.plate == vehicle.plate)
            
//             $.post(`https://${GetParentResourceName()}/setfavourite`, JSON.stringify({
//                 favourite: this.vehicles[index].favourite,
//                 plate: this.vehicles[index].plate,
//             })).then((response) => {
//                 this.vehicles[index].favourite = response
//             })
//         },

//         Replace(event) {
//             event.target.src = "assets/images/car_none.png"
//         },

//         KeyDown(event) {
//             if (event.code === "Escape") {
//                 this.Exit()
//             }
//         },

//         Exit() {
//             $(".form").fadeOut();
//             $.post(`https://${GetParentResourceName()}/exit`);
//         }
//     },
    
//     mounted() {
//         const self = this;

//         window.addEventListener('message', function(event) {
//             const item = event.data

//             if (item.action == "OpenMenu") {
//                 self.impoundprice = item.impoundprice
//                 $(".form").fadeIn();
//             } else if (item.action == "LoadVehicles") {
//                 self.locsort = item.locsort
//                 self.vehicles = item.vehicles
//                 self.currcategory = item.locsort
//             }
//         }) 

//         document.addEventListener("keydown", self.KeyDown);
//     },
// });