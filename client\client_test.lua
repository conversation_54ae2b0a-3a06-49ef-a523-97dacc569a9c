-- Test Client für Garage Interaktion
-- Diese <PERSON>i kann verwendet werden, um zu testen, ob die Garage-Interaktion funktioniert

local isNearGarage = false
local currentGarage = nil
local garageBlips = {}

-- UI Status
local isUIOpen = false

-- <PERSON><PERSON><PERSON> Blips für alle Garagen
Citizen.CreateThread(function()
    for k, garage in pairs(CONFIG.Locations) do
        if garage.blip then
            local blip = AddBlipForCoord(garage.coords.x, garage.coords.y, garage.coords.z)
            local blipConfig = CONFIG.Blips[garage.type]
            
            if blipConfig then
                SetBlipSprite(blip, blipConfig.sprite)
                SetBlipScale(blip, blipConfig.scale)
                SetBlipColour(blip, blipConfig.color)
                SetBlipDisplay(blip, blipConfig.display)
                SetBlipAsShortRange(blip, blipConfig.shortrange)
                BeginTextCommandSetBlipName("STRING")
                AddTextComponentString(blipConfig.text)
                EndTextCommandSetBlipName(blip)
            end
            
            table.insert(garageBlips, blip)
        end
    end
end)

-- Hauptschleife für Garage-Interaktion
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        
        local playerPed = PlayerPedId()
        local playerCoords = GetEntityCoords(playerPed)
        local nearGarage = false
        
        -- Überprüfe alle Garagen-Standorte
        for k, garage in pairs(CONFIG.Locations) do
            local distance = #(playerCoords - garage.coords)
            
            if distance < 3.0 then
                nearGarage = true
                currentGarage = garage
                
                -- Zeige Hilfetext
                if not isUIOpen then
                    DrawText3D(garage.coords.x, garage.coords.y, garage.coords.z + 1.0, "[E] Garage öffnen")
                end
                
                -- Überprüfe E-Taste
                if IsControlJustPressed(0, 38) and not isUIOpen then -- E-Taste
                    print("E-Taste gedrückt bei Garage: " .. tostring(k))
                    OpenGarageUI(garage)
                end
                
                break
            end
        end
        
        isNearGarage = nearGarage
        
        if not nearGarage then
            currentGarage = nil
        end
        
        -- Wenn nicht in der Nähe einer Garage, weniger oft überprüfen
        if not nearGarage then
            Citizen.Wait(500)
        end
    end
end)

-- 3D Text Funktion
function DrawText3D(x, y, z, text)
    local onScreen, _x, _y = World3dToScreen2d(x, y, z)
    local px, py, pz = table.unpack(GetGameplayCamCoords())
    
    if onScreen then
        SetTextScale(0.35, 0.35)
        SetTextFont(4)
        SetTextProportional(1)
        SetTextColour(255, 255, 255, 215)
        SetTextEntry("STRING")
        SetTextCentre(1)
        AddTextComponentString(text)
        DrawText(_x, _y)
        
        local factor = (string.len(text)) / 370
        DrawRect(_x, _y + 0.0125, 0.015 + factor, 0.03, 41, 11, 41, 68)
    end
end

-- Garage UI öffnen
function OpenGarageUI(garage)
    print("Öffne Garage UI für Typ: " .. garage.type .. " Sort: " .. garage.sort)

    isUIOpen = true
    SetNuiFocus(true, true)

    -- Sende Daten an HTML
    SendNUIMessage({
        action = "OpenMenu",
        impoundprice = CONFIG.Impoundprice or 1000
    })

    -- Lade Fahrzeuge vom Server
    TriggerServerEvent('garage:loadVehicles', garage.type, garage.sort)
end

-- NUI Callbacks
RegisterNUICallback('exit', function(data, cb)
    print("Garage UI geschlossen")
    isUIOpen = false
    SetNuiFocus(false, false)
    cb('ok')
end)

RegisterNUICallback('parkoutvehicle', function(data, cb)
    print("Fahrzeug ausparken: " .. json.encode(data))

    -- Finde einen freien Spawn-Punkt
    local spawnCoords = nil
    if currentGarage and currentGarage.spawns then
        for _, spawn in pairs(currentGarage.spawns) do
            -- Hier würdest du prüfen, ob der Spawn-Punkt frei ist
            spawnCoords = spawn.coords
            break
        end
    end

    if spawnCoords then
        TriggerServerEvent('garage:parkOutVehicle', data.vehicle, spawnCoords)
    else
        print("Kein freier Spawn-Punkt gefunden!")
    end

    cb('ok')
end)

RegisterNUICallback('parkinvehicle', function(data, cb)
    print("Fahrzeug einparken: " .. json.encode(data))
    -- Hier würdest du das Fahrzeug einparken
    cb('ok')
end)

RegisterNUICallback('switchcategory', function(data, cb)
    print("Kategorie wechseln zu: " .. data.category)
    -- Hier würdest du die Fahrzeuge für die neue Kategorie laden
    cb('ok')
end)

RegisterNUICallback('changenickname', function(data, cb)
    print("Nickname ändern: " .. json.encode(data))
    cb('ok')
end)

RegisterNUICallback('setfavourite', function(data, cb)
    print("Favorit setzen: " .. json.encode(data))
    cb('ok')
end)

-- ESC-Taste zum Schließen
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        
        if isUIOpen and IsControlJustPressed(0, 322) then -- ESC
            isUIOpen = false
            SetNuiFocus(false, false)
            SendNUIMessage({action = "CloseMenu"})
        end
    end
end)

-- Debug-Befehle
RegisterCommand('garage_debug', function()
    local playerCoords = GetEntityCoords(PlayerPedId())
    print("Spieler Position: " .. tostring(playerCoords))
    print("Ist in der Nähe einer Garage: " .. tostring(isNearGarage))
    if currentGarage then
        print("Aktuelle Garage: " .. json.encode(currentGarage))
    end
end)

RegisterCommand('garage_test', function()
    if currentGarage then
        OpenGarageUI(currentGarage)
    else
        print("Nicht in der Nähe einer Garage!")
    end
end)

-- Server Events
RegisterNetEvent('garage:receiveVehicles')
AddEventHandler('garage:receiveVehicles', function(vehicles, garageSort)
    print("Fahrzeuge vom Server erhalten: " .. #vehicles .. " Fahrzeuge")

    SendNUIMessage({
        action = "LoadVehicles",
        locsort = garageSort,
        vehicles = vehicles
    })
end)

RegisterNetEvent('garage:spawnVehicle')
AddEventHandler('garage:spawnVehicle', function(vehicle, spawnCoords)
    print("Spawne Fahrzeug: " .. vehicle.plate)

    -- Lade das Fahrzeugmodell
    local modelHash = GetHashKey(vehicle.modelName)
    RequestModel(modelHash)

    while not HasModelLoaded(modelHash) do
        Citizen.Wait(100)
    end

    -- Spawne das Fahrzeug
    local spawnedVehicle = CreateVehicle(modelHash, spawnCoords.x, spawnCoords.y, spawnCoords.z, spawnCoords.heading or 0.0, true, false)

    if spawnedVehicle then
        SetVehicleNumberPlateText(spawnedVehicle, vehicle.plate)
        SetEntityAsMissionEntity(spawnedVehicle, true, true)
        print("Fahrzeug erfolgreich gespawnt!")

        -- Schließe UI
        isUIOpen = false
        SetNuiFocus(false, false)
    else
        print("Fehler beim Spawnen des Fahrzeugs!")
    end

    SetModelAsNoLongerNeeded(modelHash)
end)
