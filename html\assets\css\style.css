@import url("https://fonts.cdnfonts.com/css/svn-gilroy?styles=55332,55331");
@import url("https://fonts.cdnfonts.com/css/druk-trial");
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');

:root {
    --ff-header: "SVN-Gilroy", sans-serif;
    --ff-druk: "Druk Trial", sans-serif;
    --ff-inter: "Inter", sans-serif;
    --color-white: rgba(255, 255, 255, 1);
    --color-opacity-white: rgba(255, 255, 255, 0.5);
    --border-radius-close: 0.5vh;
    --border-radius-frame: 1vh;
}

*,
*::before,
*::after {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    width: 100%;
    height: 100%;
    overflow: hidden;
    font-family: var(--ff-header);
    user-select: none;
    color: var(--color-white);

    display: none;
}

.main__genisis-header-container {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.main__genisis-header-left-stripe {
    width: 0.1vh;
    height: 3.5vh;
    background: rgba(255, 255, 255, 0.5);
    margin-right: 0.5vh;
}
.main__genisis-header-left {
    font-family: var(--ff-arame);
    font-size: 1.5vh;
}
.main__genisis-header-left p:first-child {
    font-weight: 100;
    color: #ffffff80;
    letter-spacing: 0.5vh;
    line-height: 1.5vh;
}
.main__genisis-header-left P:last-child {
    font-weight: 700;
    font-size: 2.6vh;
    color: #fff;
    line-height: 2.6vh;
}
.main__genisis-header-container-right {
    display: flex;
    align-items: center;
    gap: 1vh;
}

.main__genisis-header-btn-1 {
    display: flex;
    justify-content: center;
    padding: 1vh;
    align-items: center;
    border-radius: 0.4vh;
    height: 3.5vh;
    border: 0.1vh solid #c19117;
    background: linear-gradient(0deg, #9c6f14 0%, rgba(255, 125, 69, 0) 100%);
    box-shadow: 0 0 3.7vh #a37a09 inset, 0 0.4vh 5.6vh #ff934540;
    font-size: 1.2vh;
    color: var(--color-white);
    text-transform: uppercase;
    cursor: pointer;
    transition: 0.2s ease-in;
}
.main__genisis-header-btn-1:hover,
.main__genisis-header-btn-1.active {
    box-shadow: 0 0 1.7vh #9c6f14 inset;
}
.main__genisis-header-btn-2 {
    height: 3.5vh;
    padding: 1vh;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 0.4vh;
    border: 0.1vh solid #d939d9;
    background: radial-gradient(539.11% 270.5% at 50.17% 50%, rgba(190, 57, 217, 0.25) 0%, rgba(206, 57, 217, 0) 100%);
    box-shadow: 0 0 3.7vh #d939d9 inset, 0 0.4vh 5.6vh #b439d940;
    font-size: 1.2vh;
    color: var(--color-white);
    text-transform: uppercase;
    transition: 0.2s ease-in;
    cursor: pointer;
}
.main__genisis-header-btn-2:hover,
.main__genisis-header-btn-2.active {
    box-shadow: 0 0 1.7vh #d939d9 inset;
}

.main__genisis-header-btn-3 {
    position: relative;
    height: 3.5vh;
    padding-left: 1vh;
    padding-right: 1vh;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 0.4vh;
    border: 0.1vh solid #ffcc48;
    background: radial-gradient(539.11% 270.5% at 50.17% 50%, rgba(217, 174, 57, 0.25) 0%, rgba(217, 174, 57, 0) 100%);
    box-shadow: 0 0 3.7vh #ff9e45 inset, 0 0.4vh 5.6vh #d9ae3940;
    font-size: 1.2vh;
    color: var(--color-white);
    text-transform: uppercase;
    transition: 0.2s ease-in;
    cursor: pointer;
    gap: 0.5vh;
    overflow: hidden;
}
.main__genisis-garage-scroll-item-header-right-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1vh;
}
.main__genisis-header-btn-container {
    position: absolute;
    top: 3.25vh;
    right: 8vh;
    display: flex;
    gap: 1vh;
}
.main__genisis-header-btn-3 p {
    z-index: 100;
}
.main__genisis-header-bg {
    position: absolute;
    top: 2vh;
    left: 3vh;
}
.main__genisis-header-bg img {
    width: 8vh;
}
.main__genisis-header-btn-3-bg {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 3vh;
    color: #744519;
    transition: 0.2s ease-in;
    z-index: 0;
    opacity: 0.25;
}
.main__genisis-header-btn-3:hover .main__genisis-header-btn-3-bg,
.main__genisis-header-btn-3.active .main__genisis-header-btn-3-bg {
    transform: translate(-50%, -50%) scale(2);
    color: #8f541e;
}

.main__genisis-header-container-right-close-container {
    width: 3.5vh;
    height: 3.5vh;
    border-radius: var(--border-radius-close);
    border: 0.1vh solid rgba(255, 255, 255, 0.15);
    background: linear-gradient(0deg, rgba(255, 255, 255, 0.15) 0%, rgba(184, 28, 37, 0) 100%);
    box-shadow: 0 0 3.7vh #ffffff26 inset;
    display: flex;
    justify-content: center;
    align-items: center;
    color: var(--color-white);
    font-size: 1.5vh;
    cursor: pointer;
    transition: 0.2s ease-in;
}
.main__genisis-header-container-right-close-container i {
    transition: 0.2s ease-in;
}
.main__genisis-header-container-right-close-container:hover {
    box-shadow: 0 0 1.7vh #ffffff80 inset;
}
.main__genisis-header-container-right-close-container:hover i {
    transform: rotateY(180deg);
}

.main__genisis-garage-flex-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1.5vh;
    overflow: hidden;
}
.main__genisis-garage-container {
    width: 90vh;
    border-radius: var(--border-radius-frame);
    background: url(../images/bg.png);
    background-size: cover;
    padding: 3vh;
    overflow: hidden;
}
.main__genisis-garage-scroll-container {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1vh;
    height: 49vh;
    overflow-y: scroll;
    margin-top: 3vh;
    padding-right: 1vh;
}
.main__genisis-garage-scroll-container::-webkit-scrollbar {
    width: 0.2vh;
}
.main__genisis-garage-scroll-container::-webkit-scrollbar-thumb {
    width: 0.2vh;
    background: rgba(255, 255, 255, 0.05);
}
.main__genisis-garage-scroll-container::-webkit-scrollbar-thumb {
    width: 0.2vh;
    background: #c19117;
    box-shadow: 0 0 1.7vh #c19117;
}
.main__genisis-garage-scroll-item {
    position: relative;
    width: 100%;
    height: 24vh;
    border: 0.1vh solid rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 2.5vh #ffffff26 inset;
    padding: 1vh;
    border-radius: 0.4vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}
.main__genisis-garage-scroll-item-header {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}
.main__genisis-garage-scroll-item-header-left p:nth-child(1) {
    font-size: 2vh;
    font-family: var(--ff-druk);
    text-transform: uppercase;
    background: radial-gradient(539.11% 270.5% at 50.17% 50%, #c19117 0%, rgba(217, 174, 57, 0) 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}
.main__genisis-garage-scroll-item-header-left p:nth-child(2) {
    font-size: 1.2vh;
    color: var(--color-white);
}
.main__genisis-garage-scroll-item-header-right i {
    color: #ffffff40;
    cursor: pointer;
    transition: 0.2s ease-in;
    font-size: 1.5vh;
}
.main__genisis-garage-scroll-item-header-right:hover i,
.main__genisis-garage-scroll-item-header-right.active i {
    color: #ffcc48;
    text-shadow: 0vh 0vh 1.7vh #ffcc48;
}
main__genisis-garage-scroll-item-header-right-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1vh;
}
.main__genisis-garage-scroll-item-return-icon {
    position: absolute;
    top: 2vh;
    right: 2vh;
    color: #ffffff40;
    cursor: pointer;
    font-size: 1.6vh;
    transition: 0.2s ease-in;
}
.main__genisis-garage-scroll-item-return-icon:hover {
    color: #ff4848;
    text-shadow: 0vh 0vh 1.7vh #ff4848;
}
.main__genisis-garage-scroll-item-rename-header {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    margin-top: 3vh;
}
.main__genisis-garage-scroll-item-rename-header-icon {
    font-size: 3.25vh;
    text-transform: uppercase;
    color: #ffcc48;
    text-shadow: 0vh 0vh 1.7vh #ffcc48;
}
.main__genisis-garage-scroll-item-rename-header-small {
    font-size: 1.2vh;
    text-transform: uppercase;
    color: #ffffff8c;
    font-family: var(--ff-inter);
    font-weight: 300;
    margin-top: 1vh;
}
.rename-input {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.4vh;
    padding: 0.35vh;

    outline: none;
    border: none;
    width: 10vh;
    font-family: var(--ff-inter);
    font-size: 1.6vh;
    color: #ffffff40;
}

.rename-input::placeholder {
    color: #ffffff40;
}
.main__genisis-garage-item-window-2,
.main__genisis-garage-item-window-1 {
    position: relative;
    height: 100%;
}
.main__genisis-garage-scroll-item-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    bottom: 1.05vh;
    width: 17.8vh;
    height: 3.8vh;
    text-transform: uppercase;
    font-size: 1.2vh;
    color: var(--color-white);
    border-radius: 0.25vh;
    border: 0.1vh solid rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 3.7vh #ffffff26 inset, 0 0.4vh 5.6vh #ffffff26;
    padding: 1vh;
    cursor: pointer;
    transition: 0.2s ease-in;
}
.main__genisis-garage-scroll-item-btn:hover {
    box-shadow: 0 0 3vh #fff inset, 0 0 1.7vh #c19117;
    background: #9c6f14;
    border: 0.1vh solid transparent;
}
.main__genisis-garage-scroll-item-img {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 13vh;
}
.main__genisis-garage-scroll-item-img img {
    width: 100%;
    height: 100%;
}
.main__genisis-garage-scroll-btn {
    position: absolute;
    bottom: 1.05vh;
    width: 17.8vh;
    height: 3.8vh;
    display: flex;
    justify-content: center;
    align-items: center;
    text-transform: uppercase;
    font-size: 1.2vh;
    color: var(--color-white);
    border-radius: 0.25vh;
    border: 0.1vh solid rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 3.7vh #ffffff26 inset, 0 0.4vh 5.6vh #ffffff26;
    padding: 1vh;
    cursor: pointer;
    transition: border 0.2s ease-in, box-shadow 0.2s ease-in, background-color 0.2s ease-in;
}
.main__genisis-garage-scroll-btn:hover {
    box-shadow: 0 0 3vh #fff inset, 0 0 1.7vh #c19117;
    background: #9c6f14;
    border: 0.1vh solid transparent;
}
