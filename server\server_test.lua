-- Test Server für Garage System
-- Diese Datei implementiert die grundlegende Server-Funktionalität

-- Dummy Fahrzeug-Datenbank (normalerweise würde das aus einer echten Datenbank kommen)
local playerVehicles = {}

-- Event: Spieler möchte Fahrzeuge laden
RegisterServerEvent('garage:loadVehicles')
AddEventHandler('garage:loadVehicles', function(garageType, garageSort)
    local src = source
    local identifier = GetPlayerIdentifier(src, 0)
    
    print("Lade Fahrzeuge für Spieler: " .. identifier .. " Typ: " .. garageType .. " Sort: " .. garageSort)
    
    -- Dummy Fahrzeuge für Test
    local vehicles = {
        {
            label = "Adder",
            nickname = "Mein Sportwagen",
            modelName = "adder",
            plate = "ABC123",
            favourite = false,
            coords = nil -- Fahrzeug ist in der Garage
        },
        {
            label = "Zentorno",
            nickname = nil,
            modelName = "zentorno",
            plate = "XYZ789",
            favourite = true,
            coords = nil
        }
    }
    
    -- Sende Fahrzeuge an Client
    TriggerClientEvent('garage:receiveVehicles', src, vehicles, garageSort)
end)

-- Event: Fahrzeug ausparken
RegisterServerEvent('garage:parkOutVehicle')
AddEventHandler('garage:parkOutVehicle', function(vehicle, spawnCoords)
    local src = source
    local identifier = GetPlayerIdentifier(src, 0)
    
    print("Parke Fahrzeug aus: " .. vehicle.plate .. " für Spieler: " .. identifier)
    
    -- Hier würdest du das Fahrzeug in der Datenbank als "ausgeparkt" markieren
    -- und die Spawn-Koordinaten speichern
    
    -- Bestätige dem Client, dass das Fahrzeug gespawnt werden kann
    TriggerClientEvent('garage:spawnVehicle', src, vehicle, spawnCoords)
end)

-- Event: Fahrzeug einparken
RegisterServerEvent('garage:parkInVehicle')
AddEventHandler('garage:parkInVehicle', function(vehicle)
    local src = source
    local identifier = GetPlayerIdentifier(src, 0)
    
    print("Parke Fahrzeug ein: " .. vehicle.plate .. " für Spieler: " .. identifier)
    
    -- Hier würdest du das Fahrzeug in der Datenbank als "eingeparkt" markieren
    -- und die aktuellen Koordinaten löschen
    
    -- Bestätige dem Client, dass das Fahrzeug eingeparkt wurde
    TriggerClientEvent('garage:vehicleParked', src, vehicle)
end)

-- Event: Fahrzeug-Nickname ändern
RegisterServerEvent('garage:changeNickname')
AddEventHandler('garage:changeNickname', function(plate, nickname)
    local src = source
    local identifier = GetPlayerIdentifier(src, 0)
    
    print("Ändere Nickname für Fahrzeug: " .. plate .. " zu: " .. tostring(nickname))
    
    -- Hier würdest du den Nickname in der Datenbank aktualisieren
    
    -- Bestätige die Änderung
    TriggerClientEvent('garage:nicknameChanged', src, plate, nickname)
end)

-- Event: Favorit setzen/entfernen
RegisterServerEvent('garage:setFavourite')
AddEventHandler('garage:setFavourite', function(plate, favourite)
    local src = source
    local identifier = GetPlayerIdentifier(src, 0)
    
    print("Setze Favorit für Fahrzeug: " .. plate .. " auf: " .. tostring(favourite))
    
    -- Hier würdest du den Favorit-Status in der Datenbank aktualisieren
    
    -- Bestätige die Änderung
    TriggerClientEvent('garage:favouriteChanged', src, plate, favourite)
end)

-- Event: Fahrzeug aus Impound holen
RegisterServerEvent('garage:takeFromImpound')
AddEventHandler('garage:takeFromImpound', function(vehicle, payAmount)
    local src = source
    local identifier = GetPlayerIdentifier(src, 0)
    
    print("Hole Fahrzeug aus Impound: " .. vehicle.plate .. " Kosten: " .. payAmount)
    
    -- Hier würdest du prüfen, ob der Spieler genug Geld hat
    -- und das Fahrzeug aus dem Impound entfernen
    
    -- Für den Test nehmen wir an, dass es funktioniert
    TriggerClientEvent('garage:vehicleFromImpound', src, vehicle)
end)

-- Debug-Befehl
RegisterCommand('garage_server_debug', function(source, args, rawCommand)
    print("=== GARAGE SERVER DEBUG ===")
    print("Aktive Spieler: " .. GetNumPlayerIndices())
    print("Fahrzeug-Datenbank: " .. json.encode(playerVehicles))
end, true)

-- Hilfsfunktion: Spieler-Identifier abrufen
function GetPlayerIdentifier(source, idtype)
    local identifiers = GetPlayerIdentifiers(source)
    for _, identifier in pairs(identifiers) do
        if string.find(identifier, idtype and idtype or "steam") then
            return identifier
        end
    end
    return nil
end

print("^2[GARAGE] ^7Test Server geladen!")
