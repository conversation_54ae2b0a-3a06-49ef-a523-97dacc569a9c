# Garage Debug & Fehlerbehebung

## Problem
Die Garage-Interaktion funktioniert nicht - E-Taste reagiert nicht an den Koordinaten.

## Mögliche Ursachen
1. **Verschlüsselte Client-Datei**: Die ursprüngliche `client/client.lua` ist verschlüsselt/obfuskiert
2. **Skript läuft nicht**: Das Skript ist möglicherweise nicht richtig gestartet
3. **Koordinaten-Problem**: Die Koordinaten stimmen nicht mit der aktuellen Position überein
4. **Konflikte**: Andere Skripte interferieren mit der Garage-Funktionalität

## Test-Lösung
Ich habe Test-Dateien erstellt, um das Problem zu diagnostizieren:

### Erstellte Dateien:
- `client/client_test.lua` - Test-Client mit Debug-Funktionen
- `server/server_test.lua` - Test-Server für grundlegende Funktionalität
- `fxmanifest.lua` - Aktualisiert, um Test-<PERSON><PERSON> zu verwenden

## Schritte zur Fehlerbehebung:

### 1. Skript neu starten
```
/restart final_garage
```

### 2. Debug-Befehle verwenden
```
/garage_debug          - Zeigt aktuelle Position und Garage-Status
/garage_test          - Öffnet Garage UI (wenn in der Nähe)
/garage_server_debug  - Server-Debug-Informationen
```

### 3. F8-Konsole überprüfen
- Öffne F8-Konsole in FiveM
- Schaue nach Fehlermeldungen oder Debug-Ausgaben
- Achte auf Meldungen wie "E-Taste gedrückt bei Garage: X"

### 4. Koordinaten überprüfen
Die Garage-Standorte sind in `config/config.lua` definiert. Beispiele:
- Pizzaria: `vector3(-1440.9985, -353.8686, 43.8468)`
- Carshop: `vector3(-65.6367, -1114.3074, 26.5080)`
- Richman: `vector3(-1667.1041, 42.4934, 63.0500)`

### 5. Test-Funktionen
Der Test-Client zeigt:
- 3D-Text "[E] Garage öffnen" wenn du in der Nähe bist (3 Meter Radius)
- Debug-Ausgaben in der Konsole
- Funktionsfähige UI mit Test-Fahrzeugen

## Erwartetes Verhalten:
1. **In der Nähe einer Garage**: 3D-Text erscheint
2. **E-Taste drücken**: Konsolen-Nachricht + UI öffnet sich
3. **UI funktioniert**: Buttons reagieren, ESC schließt UI

## Wenn es immer noch nicht funktioniert:

### Überprüfe diese Punkte:
1. **Andere Skripte**: Deaktiviere andere Garage/Fahrzeug-Skripte temporär
2. **Ressourcen-Reihenfolge**: Stelle sicher, dass keine Konflikte bestehen
3. **Berechtigungen**: Überprüfe, ob das Skript die nötigen Berechtigungen hat
4. **Dependencies**: Stelle sicher, dass `oxmysql` verfügbar ist (falls benötigt)

### Alternative Koordinaten testen:
Gehe zu einer dieser Positionen und teste:
```
/tp -1440 -353 43     (Pizzaria Garage)
/tp -65 -1114 26      (Carshop Garage)
/tp -1667 42 63       (Richman Garage)
```

## Zurück zur Original-Version:
Um zur ursprünglichen verschlüsselten Version zurückzukehren:
1. Kommentiere in `fxmanifest.lua` die Test-Dateien aus
2. Entkommentiere die Original-Dateien
3. Starte das Skript neu

## Debug-Ausgaben:
Achte auf diese Konsolen-Nachrichten:
- "E-Taste gedrückt bei Garage: X"
- "Öffne Garage UI für Typ: X Sort: X"
- "Fahrzeuge vom Server erhalten: X Fahrzeuge"
- "[GARAGE] Test Server geladen!"

## Kontakt:
Falls das Problem weiterhin besteht, teile die Debug-Ausgaben aus der F8-Konsole mit.
